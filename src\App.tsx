
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import AllMovies from "./pages/AllMovies";
import AllSeries from "./pages/AllSeries";
import AllRequested from "./pages/AllRequested";
import NotFound from "./pages/NotFound";
import AdminPanel from "./pages/AdminPanel";
import Disclaimer from "./pages/Disclaimer";
import DMCA from "./pages/DMCA";
import Contact from "./pages/Contact";
import { useEffect } from "react";

const queryClient = new QueryClient();

const App = () => {
  // Apply dark theme class on app mount
  useEffect(() => {
    document.body.classList.add("dark");
    return () => document.body.classList.remove("dark");
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            <Route path="/movies" element={<AllMovies />} />
            <Route path="/series" element={<AllSeries />} />
            <Route path="/requested" element={<AllRequested />} />
            <Route path="/admin" element={<AdminPanel />} />
            <Route path="/disclaimer" element={<Disclaimer />} />
            <Route path="/dmca" element={<DMCA />} />
            <Route path="/contact" element={<Contact />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
