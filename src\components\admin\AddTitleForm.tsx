import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { Toggle } from "@/components/ui/toggle";
import { ChevronDown, FileUp, Star, Video, UploadCloud, PlusCircle, Info, CheckCircle, X, Upload } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";

const GENRES = [
  "Action", "Adventure", "Comedy", "Drama", "Fantasy", "Thriller", "Horror", "Sci-Fi", "Romance", "Crime", "Mystery", "Animation", "Family"
];
const LANGUAGES = [
  "Hindi", "English", "Tamil", "Telugu", "Malayalam", "Korean", "Japanese", "Anime"
];
const QUALITY = ["HD", "WEB", "BluRay", "Cam", "HDTS", "HDTC"];

interface FormData {
  title: string;
  type: string;
  tmdbId: string;
  year: string;
  genres: string[];
  languages: string[];
  description: string;
  posterUrl: string;
  thumbnailUrl: string;
  videoLinks: string;
  quality: string[];
  tags: string;
  imdbRating: string;
  runtime: string;
  studio: string;
  audioTracks: string[];
  trailer: string;
  subtitleFile: File | null;
  subtitleUrl: string;
  isPublished: boolean;
  isFeatured: boolean;
  addToCarousel: boolean;
}

interface Episode {
  id: string;
  title: string;
  season: number;
  episode: number;
  description: string;
  videoLink: string;
}

export default function AddTitleForm() {
  const { toast } = useToast();
  const [showPosterUrl, setShowPosterUrl] = useState(false);
  const [showThumbnailUrl, setShowThumbnailUrl] = useState(false);
  const [episodes, setEpisodes] = useState<Episode[]>([]);
  const [showEpisodeForm, setShowEpisodeForm] = useState(false);
  
  const [formData, setFormData] = useState<FormData>({
    title: "",
    type: "movie",
    tmdbId: "",
    year: "",
    genres: [],
    languages: [],
    description: "",
    posterUrl: "",
    thumbnailUrl: "",
    videoLinks: "",
    quality: [],
    tags: "",
    imdbRating: "",
    runtime: "",
    studio: "",
    audioTracks: [],
    trailer: "",
    subtitleFile: null,
    subtitleUrl: "",
    isPublished: false,
    isFeatured: false,
    addToCarousel: false,
  });

  const [newEpisode, setNewEpisode] = useState({
    title: "",
    season: 1,
    episode: 1,
    description: "",
    videoLink: "",
  });

  const [newGenre, setNewGenre] = useState("");
  const [isFetching, setIsFetching] = useState(false);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleMultiSelect = (field: keyof FormData, value: string) => {
    const currentArray = formData[field] as string[];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    handleInputChange(field, newArray);
  };

  const handleQualityToggle = (quality: string) => {
    handleMultiSelect('quality', quality);
  };

  const handleFetchFromTMDB = async () => {
    if (!formData.tmdbId) {
      toast({
        title: "Error",
        description: "Please enter a TMDB ID first",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Fetching from TMDB",
      description: "This would fetch data from TMDB API...",
    });
    
    // Simulate TMDB fetch
    setTimeout(() => {
      setFormData(prev => ({
        ...prev,
        title: "Sample Movie Title",
        year: "2024",
        description: "This is a sample description fetched from TMDB...",
        imdbRating: "8.5",
        runtime: "120",
        studio: "Sample Studios",
      }));
      
      toast({
        title: "Success",
        description: "Data fetched from TMDB successfully!",
      });
    }, 1000);
  };

  const handleFetchPosterFromTMDB = async () => {
    if (!formData.tmdbId) {
      toast({
        title: "Error",
        description: "Please enter a TMDB ID first",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Fetching Poster",
      description: "This would fetch poster from TMDB API...",
    });
    
    // Simulate TMDB fetch
    setTimeout(() => {
      setFormData(prev => ({
        ...prev,
        posterUrl: "https://example.com/poster.jpg",
      }));
      
      toast({
        title: "Success",
        description: "Poster fetched from TMDB successfully!",
      });
    }, 1000);
  };

  const handleFetchThumbnailFromTMDB = async () => {
    if (!formData.tmdbId) {
      toast({
        title: "Error",
        description: "Please enter a TMDB ID first",
        variant: "destructive",
      });
      return;
    }

    toast({
      title: "Fetching Thumbnail",
      description: "This would fetch thumbnail from TMDB API...",
    });
    
    // Simulate TMDB fetch
    setTimeout(() => {
      setFormData(prev => ({
        ...prev,
        thumbnailUrl: "https://example.com/thumbnail.jpg",
      }));
      
      toast({
        title: "Success",
        description: "Thumbnail fetched from TMDB successfully!",
      });
    }, 1000);
  };

  const handleFileUpload = (type: 'poster' | 'thumbnail' | 'subtitle') => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = type === 'subtitle' ? '.srt,.vtt' : 'image/*';
    
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        if (type === 'subtitle') {
          setFormData(prev => ({ ...prev, subtitleFile: file }));
          toast({
            title: "File uploaded",
            description: `Subtitle file "${file.name}" uploaded successfully`,
          });
        } else {
          // Simulate file upload and get URL
          const fakeUrl = `https://example.com/${type}/${file.name}`;
          handleInputChange(type === 'poster' ? 'posterUrl' : 'thumbnailUrl', fakeUrl);
          toast({
            title: "File uploaded",
            description: `${type} uploaded successfully`,
          });
        }
      }
    };
    
    input.click();
  };

  const addGenre = () => {
    if (newGenre.trim()) {
      setFormData(prev => ({ ...prev, genres: [...prev.genres, newGenre] }));
      setNewGenre("");
    }
  };

  const removeGenre = (index: number) => {
    setFormData(prev => ({ ...prev, genres: prev.genres.filter((_, i) => i !== index) }));
  };

  const addEpisode = () => {
    if (!newEpisode.title || !newEpisode.videoLink) {
      toast({
        title: "Error",
        description: "Please fill in episode title and video link",
        variant: "destructive",
      });
      return;
    }

    const episode: Episode = {
      id: Date.now().toString(),
      ...newEpisode,
    };

    setEpisodes(prev => [...prev, episode]);
    setNewEpisode({
      title: "",
      season: 1,
      episode: episodes.length + 2,
      description: "",
      videoLink: "",
    });
    
    toast({
      title: "Episode added",
      description: `Episode ${episode.episode} added successfully`,
    });
  };

  const removeEpisode = (id: string) => {
    setEpisodes(prev => prev.filter(ep => ep.id !== id));
    toast({
      title: "Episode removed",
      description: "Episode removed successfully",
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title || !formData.type) {
      toast({
        title: "Error",
        description: "Please fill in required fields (Title and Type)",
        variant: "destructive",
      });
      return;
    }

    console.log("Form Data:", formData);
    console.log("Episodes:", episodes);
    
    toast({
      title: "Success",
      description: `${formData.type} "${formData.title}" saved successfully!`,
    });

    // Reset form
    setFormData({
      title: "",
      type: "movie",
      tmdbId: "",
      year: "",
      genres: [],
      languages: [],
      description: "",
      posterUrl: "",
      thumbnailUrl: "",
      videoLinks: "",
      quality: [],
      tags: "",
      imdbRating: "",
      runtime: "",
      studio: "",
      audioTracks: [],
      trailer: "",
      subtitleFile: null,
      subtitleUrl: "",
      isPublished: false,
      isFeatured: false,
      addToCarousel: false,
    });
    setEpisodes([]);
  };

  const handleBulkAdd = () => {
    toast({
      title: "Bulk Add Mode",
      description: "This would open bulk add functionality...",
    });
  };

  const fetchIMDbRating = () => {
    toast({
      title: "Fetching IMDb Rating",
      description: "This would fetch rating from IMDb API...",
    });
  };

  return (
    <div className="space-y-6">
      {/* Basic Details Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Basic Details</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="title">Title Name *</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              placeholder="Movie or Web Series name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="type">Type *</Label>
            <Select value={formData.type} onValueChange={(value: 'movie' | 'series' | 'requested') => setFormData({ ...formData, type: value })}>
              <SelectTrigger>
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="movie">Movie</SelectItem>
                <SelectItem value="series">Web Series</SelectItem>
                <SelectItem value="requested">Requested</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="tmdb-id">TMDB ID</Label>
            <div className="flex gap-2">
              <Input
                id="tmdb-id"
                value={formData.tmdbId}
                onChange={(e) => setFormData({ ...formData, tmdbId: e.target.value })}
                placeholder="e.g. 123456"
                className="flex-1"
              />
              <Button 
                type="button"
                variant="secondary"
                onClick={handleFetchFromTMDB}
                disabled={!formData.tmdbId || isFetching}
                className="shrink-0 text-xs sm:text-sm px-2 sm:px-4"
              >
                {isFetching ? 'Fetching...' : <>
                  <span className="hidden sm:inline">Fetch</span>
                  <span className="sm:hidden">Get</span>
                </>}
              </Button>
            </div>
            <p className="text-xs text-muted-foreground">Auto-fetch details from TMDB</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="year">Year of Release</Label>
            <Input
              id="year"
              value={formData.year}
              onChange={(e) => setFormData({ ...formData, year: e.target.value })}
              placeholder="YYYY"
              maxLength={4}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="genres">Genres</Label>
          <div className="flex flex-wrap gap-2 mb-2">
            {formData.genres.map((genre, index) => (
              <span key={index} className="bg-secondary text-secondary-foreground px-2 py-1 rounded-md text-sm flex items-center gap-1">
                {genre}
                <button
                  type="button"
                  onClick={() => removeGenre(index)}
                  className="ml-1 hover:text-destructive"
                >
                  ×
                </button>
              </span>
            ))}
          </div>
          <div className="flex gap-2">
            <Input
              value={newGenre}
              onChange={(e) => setNewGenre(e.target.value)}
              placeholder="Add genre"
              onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addGenre())}
              className="flex-1"
            />
            <Button type="button" variant="outline" onClick={addGenre} className="shrink-0">
              Add
            </Button>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="description">Description</Label>
          <Textarea
            id="description"
            value={formData.description}
            onChange={(e) => setFormData({ ...formData, description: e.target.value })}
            placeholder="Enter a description..."
            rows={4}
          />
        </div>
      </section>

      {/* Poster & Thumbnails Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Poster & Thumbnails</h2>
        
        <div className="space-y-4">
          <div>
            <Label>Upload Poster</Label>
            <div className="flex flex-col sm:flex-row gap-2 mt-2">
              <Button type="button" variant="outline" className="flex items-center gap-2">
                <Upload className="w-4 h-4" />
                Upload
              </Button>
              <span className="text-sm text-muted-foreground self-center">or Paste URL</span>
              <Input
                value={formData.posterUrl}
                onChange={(e) => setFormData({ ...formData, posterUrl: e.target.value })}
                placeholder="https://image-url.com/poster.jpg"
                className="flex-1"
              />
            </div>
            <Button 
              type="button" 
              variant="link" 
              onClick={handleFetchPosterFromTMDB}
              disabled={!formData.tmdbId}
              className="mt-1 p-0 h-auto text-xs"
            >
              🎬 Fetch from TMDB
            </Button>
          </div>

          <div>
            <Label>Upload Thumbnail (optional)</Label>
            <div className="flex flex-col sm:flex-row gap-2 mt-2">
              <Button type="button" variant="outline" className="flex items-center gap-2">
                <Upload className="w-4 h-4" />
                Upload
              </Button>
              <span className="text-sm text-muted-foreground self-center">or Paste URL</span>
              <Input
                value={formData.thumbnailUrl}
                onChange={(e) => setFormData({ ...formData, thumbnailUrl: e.target.value })}
                placeholder="https://image-url.com/thumbnail.jpg"
                className="flex-1"
              />
            </div>
            <div className="mt-1">
              <Button 
                type="button" 
                variant="link" 
                onClick={handleFetchThumbnailFromTMDB}
                disabled={!formData.tmdbId}
                className="p-0 h-auto text-xs break-all"
              >
                🎬 Fetch from TMDB
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Streaming Information Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Streaming Information</h2>
        
        <div className="space-y-4">
          <div>
            <Label>Add Video Embed Link(s)</Label>
            <Textarea 
              placeholder="Paste embed iframe, e.g. //player.com/embed/..." 
              className="mt-1 min-h-[80px]"
              value={formData.videoLinks}
              onChange={(e) => handleInputChange('videoLinks', e.target.value)}
            />
            <span className="text-xs text-muted-foreground mt-1 block">Supports multiple links (add one per line)</span>
          </div>
          <div>
            <Label>Preview Player (Demo)</Label>
            <div className="rounded-lg bg-background border border-border shadow-inner mt-2 max-w-2xl">
              <iframe
                src="https://www.youtube.com/embed/dQw4w9WgXcQ"
                title="Player Preview"
                className="w-full aspect-video rounded-md"
                allowFullScreen
              />
            </div>
          </div>
          <div>
            <Label>Video Quality</Label>
            <div className="flex gap-2 flex-wrap mt-2">
              {QUALITY.map(q => (
                <Toggle 
                  key={q}
                  variant="outline" 
                  size="sm"
                  pressed={formData.quality.includes(q)}
                  onPressedChange={() => handleQualityToggle(q)}
                >
                  {q}
                </Toggle>
              ))}
            </div>
            {formData.quality.length > 0 && (
              <div className="text-sm text-muted-foreground mt-2">
                Selected: {formData.quality.join(', ')}
              </div>
            )}
          </div>
        </div>
      </section>

      {/* Metadata & Tags Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Metadata & Tags</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div>
            <Label>Tags / Keywords</Label>
            <Input 
              placeholder="Separate with commas, e.g. action, hero, comedy" 
              className="mt-1"
              value={formData.tags}
              onChange={(e) => handleInputChange('tags', e.target.value)}
            />
          </div>
          <div>
            <Label>IMDb Rating</Label>
            <div className="flex items-center gap-2 mt-1">
              <Input 
                placeholder="e.g. 8.1" 
                className="w-32"
                value={formData.imdbRating}
                onChange={(e) => handleInputChange('imdbRating', e.target.value)}
              />
              <Button type="button" variant="outline" size="sm" onClick={fetchIMDbRating}>
                Fetch
              </Button>
            </div>
          </div>
          <div>
            <Label>Runtime (minutes)</Label>
            <Input 
              placeholder="e.g. 164" 
              className="mt-1"
              value={formData.runtime}
              onChange={(e) => handleInputChange('runtime', e.target.value)}
            />
          </div>
          <div>
            <Label>Studio / Production</Label>
            <Input 
              placeholder="Disney, Warner Bros, etc." 
              className="mt-1"
              value={formData.studio}
              onChange={(e) => handleInputChange('studio', e.target.value)}
            />
          </div>
        </div>
      </section>

      {/* Additional Features Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Additional Features</h2>
        
        <div className="space-y-4">
          <div>
            <Label>Add Episodes (for Web Series)</Label>
            <div className="mt-2 space-y-4">
              <Button 
                type="button" 
                variant="outline" 
                size="sm" 
                className="flex gap-2 items-center"
                onClick={() => setShowEpisodeForm(!showEpisodeForm)}
              >
                <PlusCircle className="h-4 w-4" />
                Add Episode(s)
              </Button>
              
              {showEpisodeForm && (
                <div className="border border-border rounded-lg p-4 space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Input
                      placeholder="Episode title"
                      value={newEpisode.title}
                      onChange={(e) => setNewEpisode(prev => ({ ...prev, title: e.target.value }))}
                    />
                    <Input
                      type="number"
                      placeholder="Season"
                      value={newEpisode.season}
                      onChange={(e) => setNewEpisode(prev => ({ ...prev, season: parseInt(e.target.value) || 1 }))}
                    />
                    <Input
                      type="number"
                      placeholder="Episode number"
                      value={newEpisode.episode}
                      onChange={(e) => setNewEpisode(prev => ({ ...prev, episode: parseInt(e.target.value) || 1 }))}
                    />
                  </div>
                  <Textarea
                    placeholder="Episode description"
                    value={newEpisode.description}
                    onChange={(e) => setNewEpisode(prev => ({ ...prev, description: e.target.value }))}
                  />
                  <Input
                    placeholder="Video link"
                    value={newEpisode.videoLink}
                    onChange={(e) => setNewEpisode(prev => ({ ...prev, videoLink: e.target.value }))}
                  />
                  <Button type="button" onClick={addEpisode}>Add Episode</Button>
                </div>
              )}
              
              {episodes.length > 0 && (
                <div className="space-y-2">
                  <Label>Added Episodes:</Label>
                  {episodes.map((episode) => (
                    <div key={episode.id} className="flex items-center justify-between p-3 border border-border rounded-lg">
                      <div>
                        <span className="font-medium">S{episode.season}E{episode.episode}: {episode.title}</span>
                        {episode.description && <p className="text-sm text-muted-foreground">{episode.description}</p>}
                      </div>
                      <Button 
                        type="button" 
                        variant="ghost" 
                        size="sm"
                        onClick={() => removeEpisode(episode.id)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <Label>Upload Subtitle (SRT / VTT)</Label>
              <Button 
                type="button" 
                variant="outline" 
                size="sm" 
                className="flex items-center gap-2 mt-2"
                onClick={() => handleFileUpload('subtitle')}
              >
                <UploadCloud className="h-4 w-4" />
                Upload File
              </Button>
              {formData.subtitleFile && (
                <div className="text-sm text-muted-foreground mt-2">
                  Uploaded: {formData.subtitleFile.name}
                </div>
              )}
            </div>
            <div>
              <Label>Paste Subtitle Link (URL)</Label>
              <Input 
                className="mt-2" 
                placeholder="Paste URL..."
                value={formData.subtitleUrl}
                onChange={(e) => handleInputChange('subtitleUrl', e.target.value)}
              />
            </div>
          </div>
          
          <div>
            <Label>Audio Tracks (Language)</Label>
            <div className="mt-2 border border-border rounded-md p-2 bg-background max-h-[60px] overflow-y-auto">
              {LANGUAGES.map(language => (
                <label key={language} className="flex items-center gap-2 text-sm cursor-pointer hover:bg-accent p-1 rounded">
                  <input
                    type="checkbox"
                    checked={formData.audioTracks.includes(language)}
                    onChange={() => handleMultiSelect('audioTracks', language)}
                    className="rounded"
                  />
                  {language}
                </label>
              ))}
            </div>
          </div>
          
          <div>
            <Label>Add Trailer</Label>
            <Input 
              placeholder="YouTube link or paste TMDB trailer URL" 
              className="mt-2"
              value={formData.trailer}
              onChange={(e) => handleInputChange('trailer', e.target.value)}
            />
          </div>
        </div>
      </section>

      {/* Admin Controls Section */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold text-primary">Admin Controls</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="flex items-center justify-between p-4 border border-border rounded-lg bg-background/50">
            <div>
              <Label htmlFor="publish" className="text-base font-medium">Publish</Label>
              <p className="text-sm text-muted-foreground">Make content visible to users</p>
            </div>
            <Switch 
              id="publish" 
              checked={formData.isPublished} 
              onCheckedChange={(checked) => handleInputChange('isPublished', checked)} 
            />
          </div>
          <div className="flex items-center justify-between p-4 border border-border rounded-lg bg-background/50">
            <div>
              <Label htmlFor="featured" className="text-base font-medium">Featured</Label>
              <p className="text-sm text-muted-foreground">Show in featured section</p>
            </div>
            <Switch 
              id="featured" 
              checked={formData.isFeatured} 
              onCheckedChange={(checked) => handleInputChange('isFeatured', checked)} 
            />
          </div>
          <div className="flex items-center justify-between p-4 border border-border rounded-lg bg-background/50">
            <div>
              <Label htmlFor="home-carousel" className="text-base font-medium">Home Carousel</Label>
              <p className="text-sm text-muted-foreground">Add to homepage slider</p>
            </div>
            <Switch 
              id="home-carousel" 
              checked={formData.addToCarousel} 
              onCheckedChange={(checked) => handleInputChange('addToCarousel', checked)} 
            />
          </div>
        </div>
      </section>

      <form onSubmit={handleSubmit}>
        <div className="flex gap-4 pt-4 items-center flex-wrap">
          <Button type="submit" size="lg" className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Save Title
          </Button>
          <Button type="button" variant="secondary" onClick={handleBulkAdd}>
            Bulk Add Mode
          </Button>
          <span className="text-xs text-muted-foreground flex items-center gap-2">
            <Info className="h-3 w-3" /> 
            * Required fields. Other sections are optional and can be filled later.
          </span>
        </div>
      </form>
    </div>
  );
}
