
import { MediaItem } from "../types/media";

// Hero carousel "featured" items should be a mix of both movies & series
export const mediaData: MediaItem[] = [
  {
    id: "1",
    title: "Edge of Tomorrow",
    description: "A soldier fighting aliens gets to relive the same day over and over again, the day restarting every time he dies.",
    year: 2014,
    genres: ["Action", "Sci-Fi"],
    type: "movie",
    image: "https://image.tmdb.org/t/p/w300/2uHiFbinrCbRzXd0NUATqMoQmZC.jpg",
    coverImage: "https://image.tmdb.org/t/p/original/vf8wSMFGAU2x7l099GSuF3CH9jB.jpg"
  },
  {
    id: "4",
    title: "Inception",
    description: "A thief possesses the power to enter into the dreams of others.",
    year: 2010,
    genres: ["Action", "Thriller", "Sci-Fi"],
    type: "movie",
    image: "https://image.tmdb.org/t/p/w300/9gk7adHYeDvHkCSEqAvQNLV5Uge.jpg",
    coverImage: "https://image.tmdb.org/t/p/original/s2bT29y0ngXxxu2IA8AOzzXTRhd.jpg"
  },
  {
    id: "6",
    title: "Interstellar",
    description: "A team of explorers travel through a wormhole in space.",
    year: 2014,
    genres: ["Adventure", "Drama", "Sci-Fi"],
    type: "movie",
    image: "https://image.tmdb.org/t/p/w300/gEU2QniE6E77NI6lCU6MxlNBvIx.jpg",
    coverImage: "https://image.tmdb.org/t/p/original/xu9zaAevzQ5nnrsXN6JcahLnG4i.jpg"
  },
  {
    id: "8",
    title: "Avengers: Endgame",
    description: "After the devastating events of Infinity War, the universe is in ruins.",
    year: 2019,
    genres: ["Action", "Adventure", "Sci-Fi"],
    type: "movie",
    image: "https://image.tmdb.org/t/p/w300/or06FN3Dka5tukK1e9sl16pB3iy.jpg",
    coverImage: "https://image.tmdb.org/t/p/original/ulzhLuWrPK07P1YkdWQLZnQh1JL.jpg"
  },
  {
    id: "10",
    title: "The Matrix",
    description: "A computer hacker learns about the true nature of his reality and his role in the war against its controllers.",
    year: 1999,
    genres: ["Action", "Sci-Fi"],
    type: "movie",
    image: "https://image.tmdb.org/t/p/w300/f89U3ADr1oiB1s9GkdPOEpXUk5H.jpg",
    coverImage: "https://image.tmdb.org/t/p/original/ncEsesgOJDNrTUED89hYbA117wo.jpg"
  },

  // Dummy movie cards:
  {
    id: "11",
    title: "Dummy Movie 1",
    description: "A placeholder adventure with unexpected twists.",
    year: 2022,
    genres: ["Action", "Adventure"],
    type: "movie",
    image: "/photo-1488590528505-98d2b5aba04b",
    coverImage: "/photo-1488590528505-98d2b5aba04b"
  },
  {
    id: "12",
    title: "Dummy Movie 2",
    description: "Experience the drama of a lifetime in this fake film.",
    year: 2021,
    genres: ["Drama"],
    type: "movie",
    image: "/photo-1518770660439-4636190af475",
    coverImage: "/photo-1518770660439-4636190af475"
  },
  {
    id: "13",
    title: "Dummy Movie 3",
    description: "Fast cars, high stakes, and a digital heist.",
    year: 2023,
    genres: ["Thriller", "Sci-Fi"],
    type: "movie",
    image: "/photo-1461749280684-dccba630e2f6",
    coverImage: "/photo-1461749280684-dccba630e2f6"
  },
  {
    id: "14",
    title: "Dummy Movie 4",
    description: "Witness courage as a woman takes on a new world.",
    year: 2020,
    genres: ["Action"],
    type: "movie",
    image: "/photo-1649972904349-6e44c42644a7",
    coverImage: "/photo-1649972904349-6e44c42644a7"
  },
  {
    id: "15",
    title: "Dummy Movie 5",
    description: "An unexpected journey with lots of laughs.",
    year: 2024,
    genres: ["Comedy"],
    type: "movie",
    image: "/photo-1486312338219-ce68d2c6f44d",
    coverImage: "/photo-1486312338219-ce68d2c6f44d"
  },

  // Series
  {
    id: "2",
    title: "The Mandalorian",
    description: "A Star Wars bounty hunter travels to the outer reaches of the galaxy.",
    year: 2019,
    genres: ["Action", "Adventure", "Sci-Fi", "TV Series"],
    type: "series",
    image: "https://image.tmdb.org/t/p/w300/sWgBv7LV2PRoQgkxwlibdGXKz1S.jpg",
    coverImage: "https://image.tmdb.org/t/p/original/9ijMGlJKqcslswWUzTEwScm82Gs.jpg"
  },
  {
    id: "3",
    title: "Stranger Things",
    description: "A group of young friends witness supernatural forces and secret government exploits in their small town.",
    year: 2016,
    genres: ["Drama", "Fantasy", "Horror", "TV Series"],
    type: "series",
    image: "https://image.tmdb.org/t/p/w300/x2LSRK2Cm7MZhjluni1msVJ3wDF.jpg",
    coverImage: "https://image.tmdb.org/t/p/original/56v2KjBlU4XaOv9rVYEQypROD7P.jpg"
  },
  {
    id: "5",
    title: "Breaking Bad",
    description: "A chemistry teacher diagnosed with cancer turns to a life of crime.",
    year: 2008,
    genres: ["Crime", "Drama", "Thriller", "TV Series"],
    type: "series",
    image: "https://image.tmdb.org/t/p/w300/ggFHVNu6YYI5L9pCfOacjizRGt.jpg",
    coverImage: "https://image.tmdb.org/t/p/original/mRfZQK22H5hF4T9wSsC41O36kVb.jpg"
  },
  {
    id: "7",
    title: "The Witcher",
    description: "Geralt of Rivia, a mutated monster-hunter, journeys toward his destiny in a turbulent world.",
    year: 2019,
    genres: ["Action", "Adventure", "Fantasy", "TV Series"],
    type: "series",
    image: "https://image.tmdb.org/t/p/w300/7vjaCdMw15FEbXyLQTVa04URsPm.jpg",
    coverImage: "https://image.tmdb.org/t/p/original/fw6nLIsr1IwZ2bD2TWRgONhYH2m.jpg"
  },
  {
    id: "9",
    title: "Friends",
    description: "Follows the personal and professional lives of six twenty to thirty-something-year-old friends living in Manhattan.",
    year: 1994,
    genres: ["Comedy", "TV Series"],
    type: "series",
    image: "https://image.tmdb.org/t/p/w300/f496cm9enuEsZkSPzCwnTESEK5s.jpg",
    coverImage: "https://image.tmdb.org/t/p/original/94uQIGLo7yrrxHcKQJzXrnwV6BM.jpg"
  },

  // Dummy series cards:
  {
    id: "16",
    title: "Dummy Series 1",
    description: "A heartwarming sitcom set in the future.",
    year: 2022,
    genres: ["Comedy", "TV Series"],
    type: "series",
    image: "/photo-1486312338219-ce68d2c6f44d",
    coverImage: "/photo-1486312338219-ce68d2c6f44d"
  },
  {
    id: "17",
    title: "Dummy Series 2",
    description: "A thrilling investigation unfolds in cyberspace.",
    year: 2021,
    genres: ["Thriller", "TV Series"],
    type: "series",
    image: "/photo-1518770660439-4636190af475",
    coverImage: "/photo-1518770660439-4636190af475"
  },
  {
    id: "18",
    title: "Dummy Series 3",
    description: "Young hackers try to save their digital city.",
    year: 2020,
    genres: ["Drama", "Sci-Fi", "TV Series"],
    type: "series",
    image: "/photo-1461749280684-dccba630e2f6",
    coverImage: "/photo-1461749280684-dccba630e2f6"
  },
  {
    id: "19",
    title: "Dummy Series 4",
    description: "A medical drama unlike any you've seen before.",
    year: 2023,
    genres: ["Drama", "TV Series"],
    type: "series",
    image: "/photo-1649972904349-6e44c42644a7",
    coverImage: "/photo-1649972904349-6e44c42644a7"
  },
  {
    id: "20",
    title: "Dummy Series 5",
    description: "Time-traveling superheroes save the world again.",
    year: 2024,
    genres: ["Action", "TV Series"],
    type: "series",
    image: "/photo-1488590528505-98d2b5aba04b",
    coverImage: "/photo-1488590528505-98d2b5aba04b"
  },

  // Requested content
  {
    id: "21",
    title: "Dune: Part Two",
    description: "Paul Atreides unites with Chani and the Fremen while seeking revenge against the conspirators who destroyed his family.",
    year: 2024,
    genres: ["Action", "Adventure", "Sci-Fi"],
    type: "requested",
    image: "https://image.tmdb.org/t/p/w300/1pdfLvkbY9ohJlCjQH2CZjjYVvJ.jpg",
    coverImage: "https://image.tmdb.org/t/p/original/xOMo8BRK7PfcJv9JCnx7s5hj0PX.jpg"
  },
  {
    id: "22",
    title: "Spider-Man: No Way Home",
    description: "Peter Parker's secret identity is revealed, and he asks Doctor Strange for help. When a spell goes wrong, dangerous foes from other worlds start to appear.",
    year: 2021,
    genres: ["Action", "Adventure", "Sci-Fi"],
    type: "requested",
    image: "https://image.tmdb.org/t/p/w300/1g0dhYtq4irTY1GPXvft6k4YLjm.jpg",
    coverImage: "https://image.tmdb.org/t/p/original/14QbnygCuTO0vl7CAFmPf1fgZfV.jpg"
  },
  {
    id: "23",
    title: "The Batman",
    description: "When the Riddler, a sadistic serial killer, begins murdering key political figures in Gotham, Batman is forced to investigate the city's hidden corruption.",
    year: 2022,
    genres: ["Action", "Crime", "Drama"],
    type: "requested",
    image: "https://image.tmdb.org/t/p/w300/b0PlSFdDwbyK0cf5RxwDpaOJQvQ.jpg",
    coverImage: "https://image.tmdb.org/t/p/original/qqHQsStV6exghCM7zbObuYBiYxw.jpg"
  },
  {
    id: "24",
    title: "Top Gun: Maverick",
    description: "After thirty years, Maverick is still pushing the envelope as a top naval aviator, but must confront ghosts of his past when he leads TOP GUN's elite graduates on a mission.",
    year: 2022,
    genres: ["Action", "Drama"],
    type: "requested",
    image: "https://image.tmdb.org/t/p/w300/62HCnUTziyWcpDaBO2i1DX17ljH.jpg",
    coverImage: "https://image.tmdb.org/t/p/original/odJ4hx6g6vBt4lBWKFD1tI8WS4x.jpg"
  },
  {
    id: "25",
    title: "House of the Dragon",
    description: "The Targaryen civil war, known as the Dance of the Dragons, is about to begin. Based on George R.R. Martin's 'Fire & Blood'.",
    year: 2022,
    genres: ["Action", "Adventure", "Drama", "TV Series"],
    type: "requested",
    image: "https://image.tmdb.org/t/p/w300/z2yahl2uefxDCl0nogcRBstwruJ.jpg",
    coverImage: "https://image.tmdb.org/t/p/original/17TTFFAXcg1hKAi3TOvNnXXOhej.jpg"
  },
  {
    id: "26",
    title: "Wednesday",
    description: "Wednesday Addams is sent to Nevermore Academy, a supernatural boarding school where she attempts to master her psychic powers, thwart a monstrous killing spree.",
    year: 2022,
    genres: ["Comedy", "Crime", "Family", "TV Series"],
    type: "requested",
    image: "https://image.tmdb.org/t/p/w300/9PFonBhy4cQy7Jz20NpMygczOkv.jpg",
    coverImage: "https://image.tmdb.org/t/p/original/iHSwvRVsRyxpX7FE7GbviaDvgGZ.jpg"
  }
];
